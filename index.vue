<template>
  <div class="el-animate" v-style="state.wrap">
    <view class="layer">
      <image v-if="state.load != true" :src="value.frames[0][0].src" mode="widthFix" />
    </view>
    <view class="frame">
      <!-- 预加载开始 -->
      <view style="position: absolute; left: 0; top: 0; z-index: -1; width: 0; height: 0; opacity: 0; overflow: hidden">
        <template v-for="(item, index) in value.frames">
          <image v-for="(v, i) in item" :src="v.src" mode="widthFix" @load="fn.onWait" />
        </template>
      </view>
      <!-- 预加载结束 -->
      <view v-show="state.display" class="ul" :style="{
        height: `${state.total * 100}%`,
        animationName: `${animationName}`,
        animationDuration: `${value.duration}s`,
        animationIterationCount: `${value.loop ? 'infinite' : 1}`,
        animationTimingFunction: `steps(${state.total}, end)`,
      }" @animationend="fn.onAnimationEnd">
        <block v-for="(item, index) in value.frames">
          <view class="li"
            :style="{ height: `${(1 / state.total) * 100}%`, top: `${index * (1 / state.total) * 100}%` }">
            <image v-for="(v, i) in item" :src="v.src" mode="widthFix" />
          </view>
        </block>
        <view v-if="!value.loop" class="li"
          :style="{ top: `${state.total * (1 / state.total) * 100}%`, height: `${(1 / state.total) * 100}%` }">
          <image v-for="(v, i) in value.frames[state.total - 1]" :src="v.src" mode="widthFix" />
        </view>
      </view>
    </view>
  </div>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import './index.css';
const props = defineProps({
  animationName: { default: 'keyframe' },
  value: {
    default: {
      left: 0,
      top: 0,
      width: 550,
      height: 250,
      scale: 1,
      rotate: 0,
      duration: 2, //持续时间
      loop: true, //是否循环
      frames: [
        [{ src: 'https://img.alicdn.com/imgextra/i2/807589455/O1CN01UA75sy2JiRYKOME06_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i3/807589455/O1CN011GmeU62JiRYTu0Dkq_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i1/807589455/O1CN01MgnNjs2JiRYRuE9CK_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i3/807589455/O1CN01TAIpFZ2JiRYWdj8tm_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i4/807589455/O1CN01wb46sC2JiRYL5ASAQ_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i1/807589455/O1CN01fwoHAd2JiRYSgjIyU_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i2/807589455/O1CN01Hu769B2JiRYVqaNfh_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i2/807589455/O1CN01WinKgF2JiRYPE2YTK_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i4/807589455/O1CN01msq9bU2JiRYWdi8WS_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i2/807589455/O1CN01uS0MKc2JiRYRuDPS1_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i1/807589455/O1CN01JfJDf52JiRYIq2Lcy_!!807589455.png' }],
        [{ src: 'https://img.alicdn.com/imgextra/i1/807589455/O1CN01adbXQB2JiRYNNTFxe_!!807589455.png' }],
      ],
    },
  },
  onInit: { default: () => null },
  onTap: { default: () => null },
  onEnd: { default: () => null },
});
const state = reactive({
  display: false,
  total: 0,
  count: 0,
});
const fn = {
  didInit() {
    const { value } = props;
    const { frames = [] } = value;
    state.total = frames.length;
    state.count = 0;
    state.wrap = {
      left: value.left,
      top: value.top,
      width: `${value.width}rpx`,
      height: `${value.height}rpx`,
      transform: `rotate(${value.rotate}deg) scale(${value.scale})`,
    };
  },
  onWait() {
    state.count++;
    if (state.count >= state.total) {
      state.display = true;
      state.load = true;
    }
  },
  onAnimationEnd(e) {
    props.onEnd(props);
  },
};
onMounted(() => {
  fn.didInit();
});
</script>
