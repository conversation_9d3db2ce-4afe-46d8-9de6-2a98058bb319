.el-animate { position: absolute; left: 0; top: 0; min-width: 10rpx; min-height: 10rpx; overflow: hidden;}
.el-animate image { display: block; width: 100%; height: 100%; font-size: 0;}
.el-animate .frame { position: relative; width: 100%; height: 100%; overflow: hidden;}
.el-animate .frame .ul { position: relative; width: 100%; animation-fill-mode: forwards;}
.el-animate .frame .li { position: absolute; width: 100%; height: 100%; overflow: hidden;}
.el-animate .layer { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9;}
@keyframes keyframe { 
   0% { transform: translateY(0);}
   100% { transform: translateY(-100%);}
}
